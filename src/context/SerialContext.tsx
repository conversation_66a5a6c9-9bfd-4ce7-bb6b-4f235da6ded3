import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from '@/components/ui/use-toast';

// Type definitions for our serial data
export type ForceData = {
  fx: number;
  fy: number;
  fz: number;
  cx: number;
  cy: number;
  rawData: number[];
  timestamp: number;
  sampleTimestamp: number; // microseconds, from device
  sampleId: number;        // sample counter, from device
};

export type ForcePlate = {
  id: string;
  port: SerialPort | null;
  reader: ReadableStreamDefaultReader<Uint8Array> | null;
  name: string;
  isConnected: boolean;
  isReading: boolean;
  data: ForceData[];
  lastData: ForceData | null;
};

type SerialContextType = {
  availablePorts: SerialPort[];
  forcePlates: ForcePlate[];
  isScanning: boolean;
  scanForPorts: () => Promise<void>;
  connectToPort: (portInfo: SerialPort) => Promise<ForcePlate | null>;
  disconnectPort: (plateId: string) => Promise<void>;
  startReading: (plateId: string) => Promise<void>;
  stopReading: (plateId: string) => Promise<void>;
  saveData: (plateId: string, fileName?: string) => Promise<void>;
  clearData: (plateId: string) => void;
};

const SerialContext = createContext<SerialContextType | undefined>(undefined);

type SerialProviderProps = {
  children: ReactNode;
};

export const SerialProvider = ({ children }: SerialProviderProps) => {
  const [availablePorts, setAvailablePorts] = useState<SerialPort[]>([]);
  const [forcePlates, setForcePlates] = useState<ForcePlate[]>([]);
  const [isScanning, setIsScanning] = useState(false);

  // Check if serial is supported
  const isSerialSupported = 'serial' in navigator;

  // Helper to get a persistent identifier for a port (usbVendorId + usbProductId + serialNumber if available)
  function getPortPersistentId(port: SerialPort): string {
    const info = port.getInfo();
    // Some browsers may not provide serialNumber, fallback to vendor/product
    return [info.usbVendorId, info.usbProductId, info.serialNumber || ''].join('-');
  }

  // Scan for available serial ports
  const scanForPorts = async () => {
    if (!isSerialSupported) {
      toast({
        title: "Serial API not supported",
        description: "Your browser doesn't support the Web Serial API. Try using Chrome.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsScanning(true);
      const ports = await navigator.serial.getPorts();
      setAvailablePorts(ports);
    } catch (error) {
      console.error('Error scanning for ports:', error);
      toast({
        title: "Port Scanning Failed",
        description: "Failed to scan for serial ports. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Connect to a serial port
  const connectToPort = async (portInfo: SerialPort): Promise<ForcePlate | null> => {
    try {
      // Request port access if needed
      const port = portInfo || await navigator.serial.requestPort();
      await port.open({ baudRate: 115200 });

      const persistentId = getPortPersistentId(port);
      // Try to find an existing plate with this persistentId (even if disconnected)
      let existingPlate = forcePlates.find(p => p.port && getPortPersistentId(p.port) === persistentId);
      if (!existingPlate) {
        // Also check disconnected plates by storing persistentId in id
        existingPlate = forcePlates.find(p => p.id === persistentId);
      }

      if (existingPlate) {
        // Reuse the existing plate (restore port, set connected)
        setForcePlates(prev => prev.map(p =>
          p === existingPlate
            ? { ...p, port, isConnected: true, name: port.getInfo().usbProductName || p.name }
            : p
        ));
        toast({
          title: "Reconnected",
          description: `Reconnected to ${existingPlate.name}`,
        });
        return { ...existingPlate, port, isConnected: true };
      }

      // Otherwise, create a new plate
      const plateId = persistentId;
      const newPlate: ForcePlate = {
        id: plateId,
        port: port,
        reader: null,
        name: port.getInfo().usbProductName || `Force Plate ${forcePlates.length + 1}`,
        isConnected: true,
        isReading: false,
        data: [],
        lastData: null,
      };
      setForcePlates((prev) => [...prev, newPlate]);
      toast({
        title: "Connection Successful",
        description: `Connected to ${newPlate.name}`,
      });
      return newPlate;
    } catch (error) {
      console.error('Error connecting to port:', error);
      toast({
        title: "Connection Failed",
        description: "Failed to connect to the serial port. Please try again.",
        variant: "destructive",
      });
      return null;
    }
  };

  // Disconnect from a serial port
  const disconnectPort = async (plateId: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate) return;

    try {
      if (plate.isReading) {
        await stopReading(plateId);
      }
      
      if (plate.port && plate.isConnected) {
        await plate.port.close();
      }

      setForcePlates(plates => 
        plates.map(p => 
          p.id === plateId ? { ...p, isConnected: false, port: null } : p
        )
      );

      toast({
        title: "Disconnected",
        description: `Disconnected from ${plate.name}`,
      });
    } catch (error) {
      console.error('Error disconnecting port:', error);
      toast({
        title: "Disconnection Failed",
        description: "Failed to properly disconnect. The port might still be in use.",
        variant: "destructive",
      });
    }
  };

  // Parse binary data from force plate
  const parseForceData = (dataBuffer: Uint8Array): ForceData | null => {
    try {
      // 2 int32 (8 bytes) + 8 ints (32 bytes) + 5 floats (20 bytes) + 2 termination chars = 62 bytes
      if (dataBuffer.length < 62) {
        console.warn('Expected 62 bytes, got:', dataBuffer.length);
        return null;
      }
      const dataView = new DataView(dataBuffer.buffer, dataBuffer.byteOffset, dataBuffer.byteLength);
      // Parse the 2 int32s (timestamp, sampleId)
      const sampleTimestamp = dataView.getInt32(0, true);
      const sampleId = dataView.getInt32(4, true);
      // Parse the 8 ints (raw load cell readings)
      const rawData = [];
      for (let i = 0; i < 8; i++) {
        rawData.push(dataView.getInt32(8 + (i * 4), true));
      }
      // Parse the 5 floats (Fx, Fy, Fz, Cx, Cy)
      const fx = dataView.getFloat32(40, true);
      const fy = dataView.getFloat32(44, true);
      const fz = dataView.getFloat32(48, true);
      const cx = dataView.getFloat32(52, true);
      const cy = dataView.getFloat32(56, true);

      // Check termination chars (\r\n)
      if (dataBuffer[60] !== 13 || dataBuffer[61] !== 10) {
        console.warn('Invalid termination chars in frame');
        // print the raw data for debugging
        console.log('Raw data:', dataBuffer);
        // print the raw data as hex
        console.log('Raw data (hex):', Array.from(dataBuffer).map(b => b.toString(16).padStart(2, '0')).join(' '));
        return null;
      }
      return {
        fx, fy, fz, cx, cy,
        rawData,
        timestamp: Date.now(),
        sampleTimestamp,
        sampleId,
      };
    } catch (error) {
      console.error('Error parsing force data:', error);
      return null;
    }
  };

  // Start reading data from a force plate
  const startReading = async (plateId: string) => {
    const plateIndex = forcePlates.findIndex(p => p.id === plateId);
    if (plateIndex === -1) return;
    
    const plate = forcePlates[plateIndex];
    if (!plate.port || !plate.isConnected || plate.isReading) return;

    try {
      const reader = plate.port.readable!.getReader();

      // Send an 's' command to the device to start sending data
      const encoder = new TextEncoder();
      const writer = plate.port.writable?.getWriter();
      if (writer) {
        await writer.write(encoder.encode('s'));
        writer.releaseLock();
      }
      
      setForcePlates(plates => 
        plates.map((p, i) => 
          i === plateIndex ? { ...p, reader, isReading: true } : p
        )
      );

      // Start the reading loop
      readLoop(plateId, reader);
      
      toast({
        title: "Data Streaming Started",
        description: `Now receiving data from ${plate.name}`,
      });
    } catch (error) {
      console.error('Error starting data read:', error);
      toast({
        title: "Read Failed",
        description: "Failed to start reading data from the force plate.",
        variant: "destructive",
      });
    }
  };

  // Loop to continuously read data
  const readLoop = async (plateId: string, reader: ReadableStreamDefaultReader<Uint8Array>) => {
    try {
      // Create a buffer for the incoming data
      let buffer = new Uint8Array(0);
      // Keep reading until stopped
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        // Append the new data to our buffer
        const newBuffer = new Uint8Array(buffer.length + value.length);
        newBuffer.set(buffer);
        newBuffer.set(value, buffer.length);
        buffer = newBuffer;
        // Process complete frames (62 bytes each, ending with \r\n)
        while (buffer.length >= 62) {
          // Search for a valid frame ending with \r\n
          let frameStart = -1;
          for (let i = 0; i <= buffer.length - 62; i++) {
            if (buffer[i + 60] === 13 && buffer[i + 61] === 10) {
              frameStart = i;
              break;
            }
          }
          if (frameStart === -1) {
            // No valid frame found, keep the last 61 bytes in case the next chunk completes a frame
            buffer = buffer.slice(-61);
            break;
          }
          const frameData = buffer.slice(frameStart, frameStart + 62);
          buffer = buffer.slice(frameStart + 62);
          const parsedData = parseForceData(frameData);
          if (parsedData) {
            // Update the force plate data
            setForcePlates(plates => 
              plates.map(p => 
                p.id === plateId ? { 
                  ...p, 
                  lastData: parsedData,
                  data: [...p.data, parsedData]
                } : p
              )
            );
          }
        }
      }
    } catch (error) {
      console.error('Error in read loop:', error);
    } finally {
      reader.releaseLock();
      setForcePlates(plates => 
        plates.map(p => 
          p.id === plateId ? { ...p, isReading: false, reader: null } : p
        )
      );
    }
  };

  // Stop reading data from a force plate
  const stopReading = async (plateId: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate || !plate.isReading || !plate.reader) return;

    try {
      // Send a 'p' command to the device to pause sending data
      const encoder = new TextEncoder();
      const writer = plate.port.writable?.getWriter();
      if (writer) {
        await writer.write(encoder.encode('p'));
        writer.releaseLock();
      }

      await plate.reader.cancel();
      setForcePlates(plates => 
        plates.map(p => 
          p.id === plateId ? { ...p, isReading: false, reader: null } : p
        )
      );
      
      toast({
        title: "Data Streaming Stopped",
        description: `Stopped receiving data from ${plate.name}`,
      });
    } catch (error) {
      console.error('Error stopping data read:', error);
      toast({
        title: "Stop Failed",
        description: "Failed to stop reading data from the force plate.",
        variant: "destructive",
      });
    }
  };

  // Save data to file
  const saveData = async (plateId: string, fileName?: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate || plate.data.length === 0) return;

    try {
      // Create blob with raw JSON data
      const dataBlob = new Blob(
        [JSON.stringify(plate.data, null, 2)], 
        { type: 'application/json' }
      );
      
      // Create a download link
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || `${plate.name.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Data Saved",
        description: `Saved ${plate.data.length} records to file`,
      });
    } catch (error) {
      console.error('Error saving data:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save force plate data to file.",
        variant: "destructive",
      });
    }
  };

  // Clear data for a force plate
  const clearData = (plateId: string) => {
    setForcePlates(plates => 
      plates.map(p => 
        p.id === plateId ? { ...p, data: [] } : p
      )
    );
    
    toast({
      title: "Data Cleared",
      description: "Force plate data has been cleared.",
    });
  };

  // Scan for ports on initial load
  useEffect(() => {
    if (isSerialSupported) {
      scanForPorts();

      // Set up event listener for when new devices are connected
      navigator.serial.addEventListener('connect', () => {
        scanForPorts();
      });
      
      // Set up event listener for when devices are disconnected
      navigator.serial.addEventListener('disconnect', () => {
        scanForPorts();
      });
      
      // Clean up event listeners
      return () => {
        navigator.serial.removeEventListener('connect', scanForPorts);
        navigator.serial.removeEventListener('disconnect', scanForPorts);
      };
    }
  }, []);

  const contextValue: SerialContextType = {
    availablePorts,
    forcePlates,
    isScanning,
    scanForPorts,
    connectToPort,
    disconnectPort,
    startReading,
    stopReading,
    saveData,
    clearData,
  };

  return (
    <SerialContext.Provider value={contextValue}>
      {children}
    </SerialContext.Provider>
  );
};

export const useSerial = (): SerialContextType => {
  const context = useContext(SerialContext);
  if (!context) {
    throw new Error('useSerial must be used within a SerialProvider');
  }
  return context;
};
