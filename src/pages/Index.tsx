
import React from 'react';
import { SerialProvider } from '@/context/SerialContext';
import AppSidebar from '@/components/AppSidebar';
import ForceVisualizationContainer from '@/components/ForceVisualizationContainer';
import ForcePlots from '@/components/ForcePlots';
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSerial } from '@/context/SerialContext';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Dashboard = () => {
  const { forcePlates } = useSerial();
  
  return (
    <div className="container mx-auto px-4 py-6">
      {forcePlates.length === 0 ? (
        <div className="text-center py-16">
          <h2 className="text-2xl font-bold mb-2">No Force Plates Connected</h2>
          <p className="text-muted-foreground mb-8">
            Connect a force plate to get started with visualization
          </p>
          <div className="bg-card border border-border p-8 rounded-lg max-w-lg mx-auto">
            <h3 className="text-xl font-medium mb-4">Getting Started</h3>
            <ol className="list-decimal list-inside space-y-3 text-left">
              <li>Connect your force plate device to your computer</li>
              <li>Click the "Connect Force Plate" button in the sidebar</li>
              <li>Select the appropriate port from the list</li>
              <li>Click "Start" to begin receiving and visualizing data</li>
            </ol>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">Force Plate Visualization</h2>
            <Badge variant="outline" className="text-sm">
              {forcePlates.filter(p => p.isReading).length} Active • {forcePlates.length} Connected
            </Badge>
          </div>
          
          <div className="mb-6">
            <Card className="overflow-hidden">
              <CardHeader className="bg-muted/30 py-3">
                <CardTitle className="text-md flex justify-between">
                  <span>3D Force Visualization</span>
                  <Badge variant="outline">Hover over plates in sidebar to highlight</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <ForceVisualizationContainer />
              </CardContent>
            </Card>
          </div>
          
          <ForcePlots />
        </div>
      )}
    </div>
  );
};

const Index = () => {
  return (
    <SerialProvider>
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <AppSidebar />
          <div className="flex-1">
            <Dashboard />
          </div>
        </div>
      </SidebarProvider>
    </SerialProvider>
  );
};

export default Index;
